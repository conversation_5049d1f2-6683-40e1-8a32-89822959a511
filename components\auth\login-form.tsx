'use client';

import { ROLE_EMPLOYEE } from '@/lib/constants';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Eye, EyeOff, Mail, Lock, Linkedin } from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useUserActivity } from '@/hooks/useUserActivity';
import { CredentialResponse, GoogleLogin } from '@react-oauth/google';
import { googleSignin } from '@/store/slices/authSlice';
import { getDomainVerificationStatus } from '@/store/slices/employeeSlice';
import { useAppDispatch } from '@/store';
import toast from 'react-hot-toast';
import { AuthResponse } from '@/types/auth';
import { useRouter } from 'next/navigation';

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

interface LoginFormProps {
  onSubmit: (data: LoginFormData) => Promise<void>;
  onLinkedInSignIn: () => Promise<void>;
  isLoading?: boolean;
}

export function LoginForm({
  onSubmit,
  onLinkedInSignIn,
  isLoading = false,
}: LoginFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [socialLoading, setSocialLoading] = useState<
    'google' | 'linkedin' | null
  >(null);
  const { trackFormSubmission, trackButtonClick } = useUserActivity();
  const dispatch = useAppDispatch();
  const router = useRouter();

  const handleGoogleLoginSuccess = async (
    credentialResponse: CredentialResponse
  ) => {
    try {
      const { credential } = credentialResponse;
      if (!credential) {
        toast.error('Google credential missing.');
        return;
      }
      const resultAction = await dispatch(
        googleSignin({ idToken: credential })
      );
      if (googleSignin.fulfilled.match(resultAction)) {
        const result = resultAction.payload as AuthResponse;
        toast.success('Login successful!');
        if (result?.data?.access_token) {
          localStorage.setItem('authToken', result.data.access_token);
        }
        const user = result.data.user;
        const roleName =
          (typeof window !== 'undefined' && localStorage.getItem('roleName')) ||
          user?.role?.name?.toLowerCase() ||
          '';

        // For employees, check domain verification status
        if (roleName.toLowerCase() === ROLE_EMPLOYEE) {
          try {
            const domainStatusResult = await dispatch(
              getDomainVerificationStatus()
            ).unwrap();
            // If domainVerified is false or null (no record exists), redirect to verification
            if (domainStatusResult?.domainVerified !== true) {
              setTimeout(() => {
                router.replace('/verify-domain');
              }, 1500);
              return;
            }
          } catch (domainError) {
            console.error(
              'Error checking domain verification status:',
              domainError
            );
            // If we can't check domain status, assume unverified and redirect
            setTimeout(() => {
              router.replace('/verify-domain');
            }, 1500);
            return;
          }
        }

        setTimeout(() => {
          router.replace('/dashboard');
        }, 1500);
      } else {
        // When googleSignin is rejected, payload is a string from rejectWithValue
        const error = resultAction.payload as string;
        const errorMsg = error || 'Login failed.';
        toast.error(errorMsg);
      }
    } catch (error: any) {
      toast.error(
        error?.message || error?.data?.message || 'Google Login failed.'
      );
    }
  };

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const handleLinkedInSignIn = async () => {
    trackButtonClick('LinkedIn Sign In', 'login-form');
    setSocialLoading('linkedin');
    try {
      await onLinkedInSignIn();
    } finally {
      setSocialLoading(null);
    }
  };

  const handleFormSubmit = async (data: LoginFormData) => {
    try {
      await onSubmit(data);
      trackFormSubmission('Login Form', true);
    } catch (error) {
      trackFormSubmission('Login Form', false);
      throw error;
    }
  };

  const isFormLoading = isLoading || isSubmitting;

  return (
    <div className="w-full max-w-md p-8 rounded-2xl shadow-2xl border-0 bg-white">
      <div className="text-xs font-semibold text-gray-500 tracking-widest mb-2 uppercase text-left">
        WELCOME
      </div>
      <div className="text-3xl font-extrabold text-gray-900 mb-2 text-left">
        Log In to your Account
      </div>
      <div className="text-lg text-gray-500 mb-6 text-left">
        Sign in to your account to continue
      </div>
      {/* Email/Password Form */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium">
            Email
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400" />
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              className={cn(
                'pl-12 h-12 rounded-lg text-base shadow-sm',
                errors.email && 'border-red-500 focus-visible:ring-red-500'
              )}
              {...register('email')}
            />
          </div>
          {errors.email && (
            <p className="text-sm text-red-500">{errors.email.message}</p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="password" className="text-sm font-medium">
            Password
          </Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400" />
            <Input
              id="password"
              type={showPassword ? 'text' : 'password'}
              placeholder="Enter your password"
              className={cn(
                'pl-12 pr-12 h-12 rounded-lg text-base shadow-sm',
                errors.password && 'border-red-500 focus-visible:ring-red-500'
              )}
              {...register('password')}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2"
              onClick={() => setShowPassword(!showPassword)}
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 text-slate-400" />
              ) : (
                <Eye className="h-5 w-5 text-slate-400" />
              )}
            </Button>
          </div>
          {errors.password && (
            <p className="text-sm text-red-500">{errors.password.message}</p>
          )}
        </div>
        <div className="flex items-center justify-between mb-2">
          <label className="flex items-center gap-2 text-sm text-gray-700">
            <input
              type="checkbox"
              className="accent-[#1976F6] w-4 h-4 rounded"
            />{' '}
            Remember me
          </label>
          <Link
            href="/auth/forgot-password"
            className="text-[#1976F6] text-sm hover:underline"
          >
            Forgot Password?
          </Link>
        </div>
        <Button
          type="submit"
          className="w-full h-12 rounded-full bg-gradient-to-r from-[#1976F6] to-[#4F8DFD] text-lg font-bold border-0 shadow-md hover:from-[#0858F8] hover:to-[#1976F6]"
          disabled={isFormLoading}
        >
          {isFormLoading ? (
            <LoadingSpinner size="sm" className="mr-2" />
          ) : (
            'Sign In'
          )}
        </Button>
      </form>
      {/* Divider */}
      <div className="flex items-center my-6">
        <div className="flex-1 h-px bg-gray-200" />
        <span className="mx-4 text-gray-400 font-medium">Or</span>
        <div className="flex-1 h-px bg-gray-200" />
      </div>
      {/* Social Buttons */}
      <div className="space-y-3 mb-4">
        <GoogleLogin
          onSuccess={handleGoogleLoginSuccess}
          onError={() => toast.error('Google login failed.')}
          width="100%"
          shape="pill"
          text="signin_with"
          theme="outline"
          logo_alignment="left"
          useOneTap={false}
        />

        <Button
          type="button"
          className="w-full h-12 bg-white text-[#0D47A1] border border-gray-300 rounded-full flex items-center justify-center gap-2 shadow-sm hover:bg-gray-100 transition-colors"
          onClick={handleLinkedInSignIn}
          disabled={!!socialLoading}
        >
          {socialLoading === 'linkedin' ? (
            <LoadingSpinner size="sm" className="mr-2" />
          ) : (
            <Linkedin className="h-5 w-5 text-blue-600" />
          )}
          Sign in with LinkedIn
        </Button>
      </div>
      {/* Sign up link */}
      <div className="text-center text-sm text-gray-600 mt-2">
        Don&apos;t have an account?{' '}
        <Link
          href="/auth/register"
          className="text-[#1976F6] font-semibold hover:underline"
        >
          Sign up
        </Link>
      </div>
    </div>
  );
}
