import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  DomainVerification,
  DomainVerificationDocument,
} from './schemas/domain-verification.schema';
import { CreateJobDto } from './dto/create-job.dto';
import { Job, JobDocument } from './schemas/job.schema';
import {
  EmployerProfile,
  EmployerProfileDocument,
} from './schemas/employer-profile.schema';
import { MailService } from '../mailer/mailer.service';
import { ConfigService } from '@nestjs/config';
import { v4 as uuid } from 'uuid';
import { BadRequestException } from '@nestjs/common';
import { ERROR } from '../../utils/error-code';
import { ROLE } from 'src/utils/constants';

@Injectable()
export class EmployeeService {
  constructor(
    @InjectModel(DomainVerification.name)
    private readonly domainModel: Model<DomainVerificationDocument>,
    @InjectModel(Job.name)
    private readonly jobModel: Model<JobDocument>,
    @InjectModel(EmployerProfile.name)
    private employerProfileModel: Model<EmployerProfileDocument>,
  ) {}

  async save(data: Partial<DomainVerification>): Promise<DomainVerification> {
    try {
      const query = { $or: [{ userId: data.userId }, { email: data.email }] };
      const existing = await this.domainModel.findOne(query);
      if (existing) {
        if (existing.verified && data.verified !== true) {
          return existing;
        }
        Object.assign(existing, data);
        await existing.save();
        return existing;
      }
      return this.domainModel.create(data);
    } catch (error) {
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async findByToken(token: string): Promise<DomainVerification | null> {
    try {
      return this.domainModel.findOne({ token });
    } catch (error) {
      console.error('Find by token error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async findDomainVerificationByUserId(
    userId: string,
  ): Promise<DomainVerification | null> {
    try {
      return this.domainModel.findOne({ userId }).sort({ createdAt: -1 });
    } catch (error) {
      console.error('Find domain verification by user ID error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async createJob(createJobDto: CreateJobDto): Promise<Job> {
    try {
      const totalWeight =
        createJobDto.criteria_weights.technical_skills +
        createJobDto.criteria_weights.soft_skills +
        createJobDto.criteria_weights.experience +
        createJobDto.criteria_weights.cultural_fit;
      if (totalWeight !== 100) {
        throw new Error('Total criteria weights must equal 100');
      }
      return this.jobModel.create(createJobDto);
    } catch (error) {
      console.error('Create job error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async getJobsByEmployer(employerId: string): Promise<Job[]> {
    try {
      return this.jobModel.find({ employerId });
    } catch (error) {
      console.error('Get jobs by employer error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async createEmployerProfile(
    userId: string,
    profileData: Partial<EmployerProfile>,
  ): Promise<EmployerProfile> {
    try {
      const existing = await this.employerProfileModel.findOne({ userId });
      if (existing) return existing;
      const created = new this.employerProfileModel({ ...profileData, userId });
      return created.save();
    } catch (error) {
      console.error('Create employer profile error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async requestVerification(
    email: string,
    user: { userId: string },
    mailService: MailService,
    configService: ConfigService,
  ) {
    try {
      console.log('Request verification called with email:', email);
      if (!email.includes('@')) {
        throw new BadRequestException(ERROR.INVALID_EMAIL);
      }
      const domain = email.split('@')[1];
      const token = uuid();
      const expiresAt = new Date(Date.now() + 1000 * 60 * 15); // 15 min
      const userId = user?.userId;
      console.log('Request verification called with userId:', userId);
      if (!userId) {
        throw new BadRequestException('User ID not found in request');
      }
      console.log('Request verification called with domain:', domain);
      const existing = await this.findDomainVerificationByUserId(userId);
      console.log('Request verification called with existing:', existing);
      if (existing) {
        if (existing.verified) {
          console.log('Request verification called with existing verified');
          throw new BadRequestException(ERROR.DOMAIN_ALREADY_VERIFIED);
        }
        if (existing.expiresAt > new Date()) {
          console.log('Request verification called with existing not expired');
          return {
            message: ERROR.VERIFICATION_EMAIL_ALREADY_SENT,
          };
        }
        console.log('Request verification called with existing expired');
      }
      await this.save({
        email,
        domain,
        token,
        expiresAt,
        userId: new Types.ObjectId(userId),
      });
      const frontendUrl = configService.get<string>('app.frontendUrl');
      const verificationUrl = `${frontendUrl}/verify-domain?token=${token}`;
      await mailService.requestVerification(email, verificationUrl);
      return { message: ERROR.VERIFICATION_EMAIL_SENT };
    } catch (error) {
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async verifyDomain(token: string) {
    try {
      const record = await this.findByToken(token);
      if (!record) {
        throw new BadRequestException(ERROR.INVALID_TOKEN);
      }
      if (record.verified) {
        throw new BadRequestException(ERROR.DOMAIN_ALREADY_VERIFIED);
      }
      if (record.expiresAt < new Date()) {
        throw new BadRequestException(ERROR.TOKEN_EXPIRED);
      }
      record.verified = true;
      await this.save(record);
      return {
        message: ERROR.DOMAIN_VERIFIED_SUCCESS.replace(
          '{domain}',
          record.domain,
        ),
      };
    } catch (error) {
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async createJobWithEmployer(user: any, createJobDto: CreateJobDto) {
    try {
      const employerId = user.userId || user.sub;
      const job = await this.createJob({ ...createJobDto, employerId });
      return { message: ERROR.JOB_CREATED, data: job };
    } catch (error) {
      console.error('Create job error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async getDomainVerificationStatus(user: {
    userId: string;
    role: { name: string };
  }) {
    try {
      if (!user?.userId) return { domainVerified: null };

      const roleName = user.role?.name?.toLowerCase();
      if (roleName !== ROLE.EMPLOYEE) return { domainVerified: null };

      const record = await this.findDomainVerificationByUserId(user.userId);
      return { domainVerified: Boolean(record?.verified) };
    } catch (error) {
      console.error('Get domain verification status error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }
}

export { EmployeeService as EmployerProfileService };
