import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  DomainVerification,
  DomainVerificationDocument,
} from './schemas/domain-verification.schema';
import { CreateJobDto } from './dto/create-job.dto';
import { Job, JobDocument } from './schemas/job.schema';
import {
  EmployerProfile,
  EmployerProfileDocument,
} from './schemas/employer-profile.schema';
import { MailService } from '../mailer/mailer.service';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { BadRequestException } from '@nestjs/common';
import { ERROR } from '../../utils/error-code';
import { ROLE } from 'src/utils/constants';

@Injectable()
export class EmployeeService {
  constructor(
    @InjectModel(DomainVerification.name)
    private readonly domainModel: Model<DomainVerificationDocument>,
    @InjectModel(Job.name)
    private readonly jobModel: Model<JobDocument>,
    @InjectModel(EmployerProfile.name)
    private employerProfileModel: Model<EmployerProfileDocument>,
  ) {}

  async save(data: Partial<DomainVerification>): Promise<DomainVerification> {
    try {
      console.log('Save method called with data:', {
        email: data.email,
        domain: data.domain,
        userId: data.userId,
        verified: data.verified
      });

      // Use userId as the primary identifier for finding existing records
      const existing = await this.domainModel.findOne({ userId: data.userId });

      if (existing) {
        console.log('Found existing record in save method - updating');
        // If already verified, don't allow changes unless explicitly verifying
        if (existing.verified && data.verified !== true) {
          console.log('Record already verified - returning existing');
          return existing;
        }

        // Update existing record
        Object.assign(existing, data);
        if (data.verified) {
          existing.verifiedAt = new Date();
        }
        const updated = await existing.save();
        console.log('Updated existing record successfully');
        return updated;
      }

      // Create new record
      console.log('No existing record found - creating new record');
      const newRecord = await this.domainModel.create(data);
      console.log('New record created successfully');
      return newRecord;
    } catch (error) {
      console.error('Error in save method:', error);
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async findByToken(token: string): Promise<DomainVerification | null> {
    try {
      return this.domainModel.findOne({ token });
    } catch (error) {
      console.error('Find by token error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async findDomainVerificationByUserId(
    userId: string,
  ): Promise<DomainVerification | null> {
    try {
      return this.domainModel.findOne({ userId }).sort({ createdAt: -1 });
    } catch (error) {
      console.error('Find domain verification by user ID error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async createJob(createJobDto: CreateJobDto): Promise<Job> {
    try {
      const totalWeight =
        createJobDto.criteria_weights.technical_skills +
        createJobDto.criteria_weights.soft_skills +
        createJobDto.criteria_weights.experience +
        createJobDto.criteria_weights.cultural_fit;
      if (totalWeight !== 100) {
        throw new Error('Total criteria weights must equal 100');
      }
      return this.jobModel.create(createJobDto);
    } catch (error) {
      console.error('Create job error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async getJobsByEmployer(employerId: string): Promise<Job[]> {
    try {
      return this.jobModel.find({ employerId });
    } catch (error) {
      console.error('Get jobs by employer error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async createEmployerProfile(
    userId: string,
    profileData: Partial<EmployerProfile>,
  ): Promise<EmployerProfile> {
    try {
      const existing = await this.employerProfileModel.findOne({ userId });
      if (existing) return existing;
      const created = new this.employerProfileModel({ ...profileData, userId });
      return created.save();
    } catch (error) {
      console.error('Create employer profile error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async requestVerification(
    email: string,
    user: { userId: string },
    mailService: MailService,
    configService: ConfigService,
  ) {
    try {
      console.log('Request verification called with email:', email);

      // Enhanced email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new BadRequestException(ERROR.INVALID_EMAIL);
      }

      console.log('Email regex passed');
      const domain = email.split('@')[1].toLowerCase();
      const userId = user?.userId;
console.log('User ID:', userId);
      if (!userId) {
        throw new BadRequestException('User ID not found in request');
      }

      // Check for blocked domains
      const blockedDomains = ['yahoo.com', 'hotmail.com', 'outlook.com'];
      console.log('Blocked domains:', blockedDomains);
      if (blockedDomains.includes(domain)) {
        throw new BadRequestException('Personal email domains are not allowed. Please use your company email.');
      }

      // Check rate limiting - only allow one request per user per 5 minutes
      const recentRequest = await this.domainModel.findOne({
        userId: new Types.ObjectId(userId),
        createdAt: { $gte: new Date(Date.now() - 5 * 60 * 1000) }
      });
      console.log('Recent request:', recentRequest);

      if (recentRequest) {
        throw new BadRequestException('Please wait 5 minutes before requesting another verification email.');
      }

      console.log('Rate limiting passed');
      const existing = await this.findDomainVerificationByUserId(userId);
      console.log('Existing record:', existing);

      if (existing) {
        console.log('Existing record found - checking status');
        if (existing.verified) {
          console.log('Domain already verified for user');
          throw new BadRequestException(ERROR.DOMAIN_ALREADY_VERIFIED);
        }

        // If existing record is not expired, don't send another email
        if (existing.expiresAt > new Date()) {
          console.log('Verification email already sent and not expired');
          return {
            message: ERROR.VERIFICATION_EMAIL_ALREADY_SENT,
          };
        }
        console.log('Existing record expired - will create new verification');
      } else {
        console.log('No existing record found - creating new verification for new user');
      }

      console.log('Proceeding to create/update verification record');

      // Generate cryptographically secure token
      const token = crypto.randomBytes(32).toString('hex');
      const expiresAt = new Date(Date.now() + 1000 * 60 * 15); // 15 min

      console.log('Creating verification record with token:', token.substring(0, 8) + '...');

      const savedRecord = await this.save({
        email: email.toLowerCase(),
        domain,
        token,
        expiresAt,
        userId: new Types.ObjectId(userId),
        verified: false,
      });

      console.log('Verification record saved successfully:', {
        email: savedRecord.email,
        domain: savedRecord.domain,
        verified: savedRecord.verified,
        expiresAt: savedRecord.expiresAt
      });

      const frontendUrl = configService.get<string>('app.frontendUrl');
      const verificationUrl = `${frontendUrl}/verify-domain?token=${token}`;

      console.log('Sending verification email to:', email);
      console.log('Verification URL:', verificationUrl);

      await mailService.requestVerification(email, verificationUrl);

      console.log('Verification email sent successfully');
      return { message: ERROR.VERIFICATION_EMAIL_SENT };
    } catch (error) {
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async verifyDomain(token: string) {
    try {
      // Validate token format
      if (!token || token.length !== 64) {
        throw new BadRequestException(ERROR.INVALID_TOKEN);
      }

      const record = await this.findByToken(token);
      if (!record) {
        throw new BadRequestException(ERROR.INVALID_TOKEN);
      }

      if (record.verified) {
        throw new BadRequestException(ERROR.DOMAIN_ALREADY_VERIFIED);
      }

      if (record.expiresAt < new Date()) {
        throw new BadRequestException(ERROR.TOKEN_EXPIRED);
      }

      // Increment verification attempts for security tracking
      record.verificationAttempts = (record.verificationAttempts || 0) + 1;

      // Limit verification attempts
      if (record.verificationAttempts > 5) {
        throw new BadRequestException('Too many verification attempts. Please request a new verification email.');
      }

      record.verified = true;
      record.verifiedAt = new Date();
      await this.save(record);

      return {
        message: ERROR.DOMAIN_VERIFIED_SUCCESS.replace(
          '{domain}',
          record.domain,
        ),
      };
    } catch (error) {
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async createJobWithEmployer(user: any, createJobDto: CreateJobDto) {
    try {
      const employerId = user.userId || user.sub;
      const job = await this.createJob({ ...createJobDto, employerId });
      return { message: ERROR.JOB_CREATED, data: job };
    } catch (error) {
      console.error('Create job error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async getDomainVerificationStatus(user: {
    userId: string;
    role: { name: string };
  }) {
    try {
      if (!user?.userId) return { domainVerified: null };

      const roleName = user.role?.name?.toLowerCase();
      if (roleName !== ROLE.EMPLOYEE) return { domainVerified: null };

      const record = await this.findDomainVerificationByUserId(user.userId);
      return { domainVerified: Boolean(record?.verified) };
    } catch (error) {
      console.error('Get domain verification status error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }
}

export { EmployeeService as EmployerProfileService };
