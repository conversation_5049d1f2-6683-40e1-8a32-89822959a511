import { Injectable } from '@nestjs/common';
import { MailerService as NestMailerService } from '@nestjs-modules/mailer';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class MailService {
  constructor(private readonly mailerService: NestMailerService) {}

  async sendResetPasswordMail(to: string, name: string, resetUrl: string) {
    // Try multiple paths to find the template file
    const possiblePaths = [
      path.join(__dirname, 'reset-password.template.html'), // Production path
      path.join(
        __dirname,
        '..',
        '..',
        '..',
        'src',
        'modules',
        'mailer',
        'reset-password.template.html',
      ), // Development path from dist
      path.join(
        process.cwd(),
        'src',
        'modules',
        'mailer',
        'reset-password.template.html',
      ), // Direct path from project root
    ];

    let templatePath = '';
    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        templatePath = possiblePath;
        break;
      }
    }

    if (!templatePath) {
      throw new Error('Reset password template file not found');
    }

    let html = fs.readFileSync(templatePath, 'utf8');
    html = html
      .replace(/{{name}}/g, name)
      .replace(/{{resetUrl}}/g, resetUrl)
      .replace(/{{year}}/g, new Date().getFullYear().toString());
    await this.mailerService.sendMail({
      to,
      subject: 'Reset Your Password',
      html,
    });
  }

  async requestVerification(email: string, verificationUrl: string) {
    // Try multiple paths to find the template file
    const possiblePaths = [
      path.join(__dirname, 'verify-domain.template.html'), // Production path
      path.join(
        __dirname,
        '..',
        '..',
        '..',
        'src',
        'modules',
        'mailer',
        'verify-domain.template.html',
      ), // Development path from dist
      path.join(
        process.cwd(),
        'src',
        'modules',
        'mailer',
        'verify-domain.template.html',
      ), // Direct path from project root
    ];

    let templatePath = '';
    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        templatePath = possiblePath;
        break;
      }
    }

    let html = '';

    if (templatePath) {
      console.log('Using template path:', templatePath);
      html = fs.readFileSync(templatePath, 'utf8');
    } else {
      console.warn('Template file not found, using fallback HTML');
      // Fallback HTML template
      html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>Verify Your Domain</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 600px; margin: 0 auto; }
            .button {
              display: inline-block;
              background: #0858f8;
              color: white;
              padding: 12px 24px;
              text-decoration: none;
              border-radius: 4px;
              margin: 20px 0;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>Verify Your Domain</h2>
            <p>Hello,</p>
            <p>To complete your registration, please verify your domain by clicking the button below:</p>
            <a href="{{verificationUrl}}" class="button">Verify Domain</a>
            <p>If you did not request this, you can safely ignore this email.</p>
            <p>&copy; {{year}} TalentLoop. All rights reserved.</p>
          </div>
        </body>
        </html>
      `;
    }
    html = html
      .replace(/{{verificationUrl}}/g, verificationUrl)
      .replace(/{{year}}/g, new Date().getFullYear().toString());
    await this.mailerService.sendMail({
      to: email,
      subject: 'Verify your domain',
      html,
    });
    return { message: 'Verification email sent' };
  }
}
